# match_evaluation.py
from datetime import datetime
from typing import Any, Dict, List
from config.config import MODELS_CONFIG

from models.match_analysis_models import (
    CompatibilityEvaluation,
    PositionCandidateAnalysis,
    BatchMatchAnalysis,
    CandidatePositionAnalysis,
)
from templates.candidates_templates.candidate_analysis import (
    get_candidate_analysis_prompt,
    get_batch_candidate_analysis_batch_prompt,
)
from templates.positions_templates.position_analysis import get_position_analysis_prompt

import logging
from opentelemetry import trace

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__)


from contextlib import contextmanager
import time


@contextmanager
def log_time_block(block_name):
    start_time = time.perf_counter()
    yield
    end_time = time.perf_counter()
    elapsed_time = end_time - start_time
    logger.info(f"Block '{block_name}' executed in {elapsed_time:.4f} seconds")


def evaluate_candidate(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> dict:
    with tracer.start_as_current_span("evaluate_candidate_llm") as span:
        span.set_attribute("eval.candidate_length", len(candidate))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando candidato con LLM")
        system_prompt = get_candidate_analysis_prompt()

        messages = [
            {"role": "user", "content": f"Candidate description: {candidate}"},
            {"role": "user", "content": f"Position description: {position}"},
        ]
        analysis_response = {}

        with log_time_block("Invoke LLM on evaluate candidate"):
            analysis_response = inference_with_fallback(
                task_prompt=system_prompt, 
                model_schema=PositionCandidateAnalysis, 
                user_messages=messages,
                models_order=models_order
            )
        if not analysis_response:
            logger.error("LLM analysis failed for candidate evaluation")
            return {
                "LLM_Analysis": {},
                "extra_questions": {},
                "highlights": {},
                "Score": 0.0,
            }
        analysis_response = analysis_response.model_dump() 
        score = analysis_response.get("Score", 0.0)
        try:
            score = float(score)
        except ValueError:
            score = 0.0

        logger.info(
            "Respuesta del LLM procesada",
            extra={"custom_dimensions": {"score": score}}
        )
        return analysis_response


def evaluate_position(candidate: str, position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> PositionCandidateAnalysis:
    """
    Evaluate a position for a given candidate using the LLM.
    Uses template from position_analysis.py
    """
    system_prompt = get_position_analysis_prompt()

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"Candidate description: {candidate}"},
        {"role": "user", "content": f"Position description: {position}"},
        # {"role": "user", "content": f"Initial similarity score: {initial_score}"}
    ]

    # Attempt models in order
    default_response = {
        "LLM_Analysis": {},
        "extra_questions": {},
        "highlights": {},
        "Score": 0.0,
    }

    analysis_response = _invoke_llm_with_fallback(
        messages, default_response, model_order=models_order
    )

    # Ensure Score is float
    score = analysis_response.get("Score", 0.0)
    try:
        score = float(score)
    except ValueError:
        score = 0.0

    return PositionCandidateAnalysis(
        LLM_Analysis=analysis_response.get("LLM_Analysis", {}),
        extra_questions=analysis_response.get("extra_questions", {}),
        highlights=analysis_response.get("highlights", {}),
        Score=score,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


def evaluate_candidates_batch(candidates: List[str], position: str, models_order: list = MODELS_CONFIG["position_matching_models_order"]) -> BatchMatchAnalysis:
    """
    Evaluate multiple candidates against a position in a single LLM call.
    Returns a BatchMatchAnalysis with individual analyses and overall summary.
    """
    with tracer.start_as_current_span("evaluate_candidates_batch_llm") as span:
        span.set_attribute("eval.candidates_count", len(candidates))
        span.set_attribute("eval.position_length", len(position))

        logger.info("Evaluando %d candidatos en modo batch", len(candidates))

        task_prompt = get_batch_candidate_analysis_batch_prompt()

        # Prepare candidates for batch analysis
        candidates_prompt = "\n".join([
            f"[Init Candidate] Candidate {i + 1}:\n{candidate} [End of Candidate]" 
            for i, candidate in enumerate(candidates)
        ])

        # Create user messages
        user_messages = [
            HumanMessage(content=f"Position Description:\n{position}"),
            HumanMessage(content=f"Candidates to Evaluate:\n{candidates_prompt}")
        ]

        # Get schema text for BatchMatchAnalysis
        schema_text = """
 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")

class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    ) #
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.") #

class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
        """

        with log_time_block("Batch LLM Analysis"):
            try:
                # Use inference_with_fallback with structured output
                result = inference_with_fallback(
                    task_prompt=task_prompt,
                    model_schema=BatchMatchAnalysis,
                    user_messages=user_messages,
                    model_schema_text=schema_text,
                    models_order=models_order
                )

                if not result:
                    raise RuntimeError("All LLM providers failed")

                # Validate and normalize scores
                for analysis in result.candidates_analysis:
                    try:
                        score = float(analysis.Score)
                        score = max(0.0, min(100.0, score))  # Clamp between 0 and 100
                        analysis.Score = score
                    except (ValueError, TypeError):
                        analysis.Score = 0.0

                logger.info(
                    "Batch analysis completed successfully",
                    extra={
                        "custom_dimensions": {
                            "candidates_count": len(candidates),
                            "analysis_count": len(result.candidates_analysis)
                        }
                    }
                )

                return result

            except Exception as e:
                logger.error("Error in batch analysis: %s", str(e), exc_info=True)
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR))
                raise RuntimeError(f"Batch analysis failed: {str(e)}")


def get_candidate_analysis_custom_prompt(candidate_text: str, processed_position: str) -> CompatibilityEvaluation:    
    # 1) Build prompt
    task_prompt = (
        f"""Act as an expert evaluator in recruiting specialized technology talent. You will analyze the compatibility between a job description and a candidate's CV. I want the result to be presented in a clear and structured table format, including the following:
        1. Percentage of compatibility with the position (based on skills, experience, education, and other key requirements). Percentage of compatibility with the position (based on skills, experience, education, and other key requirements). This is a percentage between 0.0 and 100.0. Example: 85.5, 45.1, 99.0.
        2. Recommendation: Indicate whether or not the candidate is recommended to move forward in the process, along with a brief justification.
        3. Matches Found: List in a clear and orderly manner the points on which the candidate meets the requirements in the Job Description.
        4. Missing: Indicate the key requirements or competencies from the Job Description that do not appear or are not evident in the candidate's CV.
        I will then provide you with the Job Description and CV for your analysis.\n\n"""
        f"""Job Description:\n{processed_position}\n\n"""
        f"""CV:\n{candidate_text}\n\n"""
        f"""Return the analysis in strict JSON format according to the CompatibilityEvaluation model, containing the following required fields:
        - compatibilityPercentage (float with one decimal place between 0.0 and 100.0, e.g., 83.7, 42.6 — avoid round numbers or multiples of 5)
        - recommendation (boolean)
        - justification (string)
        - matchesFound (array of strings)
        - missing (array of strings)

        Requirements:
        1. The field compatibilityPercentage **must be a float, not an integer**, and must include **exactly one decimal place** (e.g., 87.3, 46.8).
        2. **Do not round or simplify** compatibilityPercentage to multiples of 5 or integers.
        3. All fields must be filled, even if matchesFound or missing are empty arrays.
        4. Output must be valid JSON — do not include explanations or extra formatting.
        """
    )
    schema_text = get_related_class_definitions(CompatibilityEvaluation)

    # 2) Call the inference function with the task prompt
    # Using inference_with_fallback to handle multiple models and fallbacks
    analysis = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=CompatibilityEvaluation,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if analysis is None or empty
    if not analysis:
        return None
    return analysis


def get_roles_to_candidates(candidate: dict) -> List[str]:
    # 1) Build prompt
    task_prompt = (
        f"""From the candidate’s resume, output exactly five specific job titles the candidate is qualified to apply for. Return only a JSON array of strings with the job titles (e.g., ["Senior .NET Developer", "..."]). Do not include any explanations or additional fields."""
        f"""\n\nCandidate Resume:\n{candidate}\n\n"""
    )
    # 2) Call the inference function with the task prompt
    roles = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=dict,  # Expecting a simple list of strings
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        models_order=MODELS_CONFIG["default_models_order"],
    )
    # 3) Check if roles is None or empty
    if not roles:
        return None
    return roles.get("job_titles", [])


from models.llm import inference_with_fallback, get_related_class_definitions
from langchain_core.messages import HumanMessage, SystemMessage

from opentelemetry.trace import Status, StatusCode

from models.llm import models_pool
try:
    models_pool["llama4-pro"]
    print(models_pool)
    print("models_config_matching", MODELS_CONFIG["position_matching_models_order"])
    print("Models Fully Loaded")
except:
    raise Exception("Models Not Loaded")


def _invoke_llm_with_fallback(
    messages, default_response, model_order: list = MODELS_CONFIG["default_models_order"]
) -> Dict[str, Any]:
    """
    Attempt to invoke LLM models in order until one succeeds.
    Return default_response if all fail.
    The model is expected to return JSON with required fields.
    """
    print("----------------Evaluating in invoke llm with fallback--------------")
    print(model_order)
    print("=====default_response========")
    print(default_response)
    print("-----------------------------------------------------")
    for model_name in model_order:
        try:
            structured_llm = models_pool[model_name].with_structured_output(
                default_response, method="json_mode"
            )
            result = structured_llm.invoke(messages).model_dump()
            # Validate result is JSON with required keys
            if isinstance(result, dict) and all(
                k in result
                for k in ["LLM_Analysis", "extra_questions", "highlights", "Score"]
            ):
                return result
        except Exception as e:
            print(f"Model {model_name} failed: {e}")

    return default_response
