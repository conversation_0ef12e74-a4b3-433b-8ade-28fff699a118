# This file provides a prompt template for evaluating a candidate against a position.
# The LLM must return J<PERSON><PERSON> with the following fields:
# {
#   "LLM_Analysis": {...},        # <PERSON><PERSON><PERSON> with summary or reasoning
#   "extra_questions": {...},     # <PERSON>SO<PERSON> with additional clarifications or interview questions suggested
#   "highlights": {...},          # JSON with key points or standout features of the candidate for this position
#   "Score": float,               # The final matching score (0 to 10)
# }
#
# Do not include "created_at" or "updated_at" in the LLM output; these are handled by code.
# 

# Import prompts from langsmith

#
from langsmith import Client

class TemplatesAnalysis:
    def __init__(self, client: Client):
        self.client = client
        self.batch_prompt = client.pull_prompt("get_batch_candidate_analysis_batch_prompt").format()
        self.match_prompt = client.pull_prompt("get_candidate_analysis_prompt").format()

    def uptate_prompts(self):
        self.batch_prompt = self.client.pull_prompt("get_batch_candidate_analysis_batch_prompt").format()
        self.match_prompt = self.client.pull_prompt("get_candidate_analysis_prompt").format()
templatesObject = TemplatesAnalysis(Client())


def get_candidate_analysis_prompt():
    prompt = templatesObject.match_prompt
    return prompt



def get_batch_candidate_analysis_batch_prompt():
    prompt = templatesObject.batch_prompt
    return prompt
