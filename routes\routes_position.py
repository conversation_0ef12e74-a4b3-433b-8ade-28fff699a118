from typing import List
from fastapi import APIRouter, HTTPException
import psycopg2

from controllers.positions_controller import (
    create_position,
    create_position_from_raw,
    fetch_all_positions,
    get_clients,
    get_locations,
    get_position_by_id,
    get_positions_page,
    get_total_positions,
    delete_position,
    update_all_positions
)
from models.models import (
    Position,
    PositionCreate,
    PositionFilters,
    PositionRawCreate
)
from models.responses import (
    PaginationResponse
)
# from utils.match_evaluations import evaluate_candidate, evaluate_position
from opentelemetry import trace  # NEW
from opentelemetry.propagate import extract  # (Opcional, si quieres extraer contexto de algún header)
from opentelemetry.trace.status import Status, StatusCode
from utils import match_evaluations as match_functions 
# Telemetry Section
import logging

# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 

router = APIRouter()

###################################################
# Position endpoints
###################################################


# Create a new position
@router.post("/", response_model=Position)
def create_position_endpoint(position: PositionCreate):
    try:
        return create_position(position)
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while creating position: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error creating position: {str(e)}")
        raise HTTPException(status_code=500, detail="Error occurred while creating position: {}".format(str(e)))


# Create a position from raw text
@router.post("/positions_from_text", response_model=Position)
def create_position_from_raw_endpoint(position: PositionRawCreate):
    try:
        return create_position_from_raw(position)
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while creating position from raw text: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail="Error occurred while creating position: {}".format(str(e)))


# Fetch a position by its ID
@router.get("/{position_id}", response_model=Position)
def get_position_endpoint(position_id: str):
    try:        
        pos = get_position_by_id(position_id)
        if pos is None:
            raise HTTPException(status_code=404, detail="Position not found")
        return pos
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching position {position_id}: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching position {position_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while fetching position: {position_id}: {str(e)}")


# Fetch all positions
@router.get("/", response_model=List[Position])
def get_all_positions_endpoint():
    try:
        return fetch_all_positions()
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching all positions: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching all positions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while fetching positions: {str(e)}")


# Fetch positions with pagination and optional search term
@router.post("/positions_pagination/", response_model=PaginationResponse)
def fetch_positions_page(page: int, chunk_size: int = 10, filters: PositionFilters | None = None):
    try:
        positions = get_positions_page(page=page, chunk_size=chunk_size, filters=filters)
        total_positions = get_total_positions(filters=filters)

        if not positions:
            return PaginationResponse(
                total_items=0,
                items=[]
            )

        return PaginationResponse(
            total_items=total_positions,
            items=positions
        )
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while fetching positions page: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error fetching positions page: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while fetching positions: {str(e)}")


# Delete a position by its ID
@router.delete("/{position_id}")
def delete_position_endpoint(position_id: str):
    try:
        pos = get_position_by_id(position_id)
        if pos is None:
            raise HTTPException(status_code=404, detail="Position not found")
        deleted_position = delete_position(pos.id)
        if not deleted_position:
            raise HTTPException(status_code=404, detail=f"Error deleting position {position_id} or Position not found")
        return deleted_position
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while deleting position {position_id}: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error deleting position {position_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while deleting position: {position_id}: {str(e)}")


# Get distinct locations from positions
# This endpoint retrieves unique location names from the position allocations in the database.
@router.get("/get/locations/", response_model=List[str], summary="Get distinct locations from positions")
def get_locations_endpoint() -> List[str]:
    try:
        return get_locations()
    except psycopg2.Error as e:
        logger.error(f"Database error get_locations_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_locations_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_locations_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving locations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Locations: {str(e)}")


# Get distinct clients from positions
# This endpoint retrieves unique client names from the position allocations in the database.
@router.get("/get/clients/", response_model=List[str], summary="Get distinct clients from positions")
def get_clients_endpoint() -> List[str]:
    try:
        return get_clients()
    except psycopg2.Error as e:
        logger.error(f"Database error get_clients_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database Error get_clients_endpoint: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException get_clients_endpoint: {str(e.detail)}")
        raise e
    except Exception as e:
        logger.error(f"Error retrieving clients: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error Retrieving Clients: {str(e)}")


# Update all positions to ensure their embeddings are up-to-date
# This is a placeholder for the actual update logic, which might involve re-embedding positions
@router.post("/update_all_positions", response_model=List[Position])
def update_all_positions_endpoint():
    try:
        updated_positions = update_all_positions()
        if not updated_positions:
            raise HTTPException(status_code=404, detail="No positions to update or all positions are already up-to-date")
        return updated_positions
    except HTTPException as http_exc:
        logger.error(f"HTTP Exception while updating all positions: {str(http_exc.detail)}")
        raise http_exc
    except Exception as e:
        logger.error(f"Error updating all positions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error occurred while updating positions: {str(e)}")