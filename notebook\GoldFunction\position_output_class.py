#Option 2
from pydantic import BaseModel, Field
from typing import List, Optional

class Skill(BaseModel):
    skillName: str = Field(..., description="Name of the skill (e.g., Azure Data Factory).")
    skillCategory: str = Field(..., description="Category of the skill (e.g., professionalSkills, niceToHave).")
    skillLeveName: str = Field(..., description="Skill level name (e.g., Intermediate, Advanced).")
    skillScore: int = Field(..., description="Numerical score representing the skill proficiency (e.g., 1-5).")

class PositionAllocation(BaseModel):
    Name: str = Field(..., description="Name of the country or region associated with the position.")
    isoCode: str = Field(..., description="ISO code of the country or region (e.g., US, IN).")

class ReasonStatus(BaseModel):
    reason: str = Field(..., description="Reason why the position was closed or updated.")

class Seniority(BaseModel):
    name: str = Field(..., description="Seniority level of the position (e.g., Junior, Senior).")

class Position(BaseModel):
    positionName: str = Field(..., description="Name of the position (e.g., Azure Data Engineer).")
    clientName: str = Field(..., description="Name of the client offering the position.")
    jobDescription: str = Field(None, description="Detailed job description in plain text.")
    mainResponsabilities: str = Field(None, description="Main responsibilities of the position.")
    seniority: Seniority = Field(None, description="Seniority level of the position.")
    roleName: str = Field(None, description="Role associated with the position (e.g., Data Engineer).")
    projectName: str = Field(None, description="Project name the position is associated with.")
    positionTypeName: str = Field(None, description="Type of position (e.g., Full-time, Contract).")
    positionCreateDate: str = Field(None, description="Date when the position was created.")
    positionStartDate: str = Field(None, description="Start date of the position.")
    positionCloseDate: str = Field(None, description="Close date of the position, if applicable.")
    createdBy: str = Field(None, description="Name or identifier of the person who created the position.")
    reasonStatus: ReasonStatus = Field(None, description="Reason for the current status of the position.")
    openPositionSkills: List[Skill] = Field(
        None, description="List of skills required for the position. Return this as a JSON array of objects."
    )
    positionAllocations: List[PositionAllocation] = Field(
        None, description="List of geographic allocations for the position. Return this as a JSON array of objects."
    )

