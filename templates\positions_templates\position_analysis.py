# This file provides a prompt template for evaluating a position from the candidate's perspective.
# The LLM must return <PERSON><PERSON><PERSON> with the following fields:
# {
#   "LLM_Analysis": {...},        # <PERSON>SO<PERSON> with summary or reasoning about why this position is suitable for the candidate
#   "extra_questions": {...},     # JSO<PERSON> with clarifications or questions candidate might have about the position
#   "highlights": {...},          # JSON with key points or standout features of the position for this candidate
#   "Score": float,               # The final matching score (0 to 10)
# }


def get_position_analysis_prompt():
    return """
    You are an expert career advisor. 
    You have two pieces of information:
    1) Candidate description: details about a candidate.
    2) Position description: details about a position.
    Your task:
    - Analyze how suitable this position is for the given candidate.
    - Provide a JSON response only, with the following fields:
      - "LLM_Analysis": A JSON object containing your reasoning and summary of why this position might be suitable (or not).
          - Skill match analysis with coefficients and comments (Just the skills that candidates presents). You can not repeat the not matched skills.
          - Skills not matched. You can not repeat the matched skills.
      - "extra_questions": A JSON object containing any additional clarifications or questions the candidate might want to ask about the position.
      - "highlights": A JSON object summarizing key strengths or standout features of the position for this candidate.
      - "Score": A float value between 0 and 10 representing your overall assessment of how well the position matches the candidate.    
    Return only valid JSON. No extra commentary outside of the JSON structure.
    """
