# config.py
import os

DATABASE_CONFIG = {
    'user': os.getenv('DB_USER', 'default_user'),
    'password': os.getenv('DB_PASSWORD', 'default_password'),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 5432)),
    'database': os.getenv('DB_NAME', 'postgres'),
}

CANDIDATE_RESUME_PAGE_OPTIONS = {
    'page-size': 'A4',
    'margin-top': '0.25in',
    'margin-right': '0.75in',
    'margin-bottom': '0.75in',
    'margin-left': '0.75in',
    'encoding': "UTF-8",
    'no-outline': None
}

MODELS_CONFIG = {
    'default_models_order': eval(os.getenv('DEFAULT_MODELS_ORDER', '["gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"]')),
    'position_matching_models_order': eval(os.getenv('POSITION_MATCH_MODELS_ORDER', '["gpt-4o", "gpt-4o-mini","llama4-pro","llama4-light"]'))
}
